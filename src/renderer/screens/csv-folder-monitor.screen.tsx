import { useState, useEffect } from 'react';
import { Button } from '../components/button';
import { safeIpcInvoke } from '../utils/electron';

interface CsvFileInfo {
  fileName: string;
  filePath: string;
  size: number;
  lastModified: Date;
  data?: any[];
  headers?: string[];
  error?: string;
}

interface FolderWatcherStatus {
  isWatching: boolean;
  folderPath: string;
  filesDetected: CsvFileInfo[];
  lastUpdate: Date;
}

export function CsvFolderMonitorScreen() {
  const [watcherStatus, setWatcherStatus] = useState<FolderWatcherStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<CsvFileInfo | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (date: Date | string): string => {
    const d = new Date(date);
    return d.toLocaleString();
  };

  // Fetch watcher status
  const fetchWatcherStatus = async () => {
    try {
      const status = await safeIpcInvoke('get-folder-watcher-status');
      setWatcherStatus(status);
      setError('');
    } catch (err) {
      console.error('Error fetching watcher status:', err);
      setError('Failed to fetch watcher status');
    }
  };

  // Start folder watching
  const startWatching = async () => {
    try {
      setIsLoading(true);
      setError('');
      const status = await safeIpcInvoke('start-folder-watching');
      setWatcherStatus(status);
      console.log('Folder watching started:', status);
    } catch (err) {
      console.error('Error starting folder watcher:', err);
      setError('Failed to start folder watching');
    } finally {
      setIsLoading(false);
    }
  };

  // Stop folder watching
  const stopWatching = async () => {
    try {
      setIsLoading(true);
      setError('');
      const status = await safeIpcInvoke('stop-folder-watching');
      setWatcherStatus(status);
      console.log('Folder watching stopped:', status);
    } catch (err) {
      console.error('Error stopping folder watcher:', err);
      setError('Failed to stop folder watching');
    } finally {
      setIsLoading(false);
    }
  };

  // Get file details
  const getFileDetails = async (fileName: string) => {
    try {
      const fileData = await safeIpcInvoke('get-csv-file-data', fileName);
      setSelectedFile(fileData);
    } catch (err) {
      console.error('Error getting file details:', err);
      setError(`Failed to get details for ${fileName}`);
    }
  };

  // Open CSV folder in file explorer
  const openCsvFolder = async () => {
    try {
      await safeIpcInvoke('open-csv-folder');
    } catch (err) {
      console.error('Error opening CSV folder:', err);
      setError('Failed to open CSV folder');
    }
  };

  // Initialize CSV folder
  const initializeCsvFolder = async () => {
    try {
      const result = await safeIpcInvoke('initialize-csv-folder');
      console.log('CSV folder initialized:', result);
      fetchWatcherStatus(); // Refresh status after initialization
    } catch (err) {
      console.error('Error initializing CSV folder:', err);
      setError('Failed to initialize CSV folder');
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    fetchWatcherStatus();

    if (autoRefresh) {
      const interval = setInterval(fetchWatcherStatus, 3000); // Refresh every 3 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  return (
    <div className="flex flex-col gap-6 p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex flex-col items-center mb-4">
        <h1 className="text-3xl font-bold text-center mb-2">
          CSV Folder Monitor
        </h1>
        <p className="text-gray-600 text-center">
          Automatically monitors the desktop/simple_csv folder for new CSV files
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Control Panel */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Control Panel</h2>
        
        <div className="flex flex-wrap gap-4 items-center">
          <Button
            onClick={startWatching}
            disabled={isLoading || (watcherStatus?.isWatching ?? false)}
            className="bg-green-500 hover:bg-green-600"
          >
            {isLoading ? 'Starting...' : 'Start Watching'}
          </Button>
          
          <Button
            onClick={stopWatching}
            disabled={isLoading || !(watcherStatus?.isWatching ?? false)}
            variant="secondary"
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            {isLoading ? 'Stopping...' : 'Stop Watching'}
          </Button>
          
          <Button
            onClick={fetchWatcherStatus}
            disabled={isLoading}
            variant="secondary"
          >
            Refresh Status
          </Button>

          <Button
            onClick={openCsvFolder}
            disabled={isLoading}
            variant="secondary"
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            Open Folder
          </Button>

          <Button
            onClick={initializeCsvFolder}
            disabled={isLoading}
            variant="secondary"
            className="bg-purple-500 hover:bg-purple-600 text-white"
          >
            Initialize Folder
          </Button>

          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Auto-refresh</span>
          </label>
        </div>
      </div>

      {/* Status Display */}
      {watcherStatus && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Watcher Status</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Status</p>
              <p className={`font-semibold ${watcherStatus.isWatching ? 'text-green-600' : 'text-red-600'}`}>
                {watcherStatus.isWatching ? 'Watching' : 'Not Watching'}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Folder Path</p>
              <p className="font-mono text-sm break-all">{watcherStatus.folderPath}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Files Detected</p>
              <p className="font-semibold">{watcherStatus.filesDetected.length}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Last Update</p>
              <p className="text-sm">{formatDate(watcherStatus.lastUpdate)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Files List */}
      {watcherStatus && watcherStatus.filesDetected.length > 0 && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Detected CSV Files</h2>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 px-4 py-2 text-left">File Name</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Size</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Last Modified</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Rows</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Status</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {watcherStatus.filesDetected.map((file, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="border border-gray-300 px-4 py-2 font-mono text-sm">
                      {file.fileName}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      {formatFileSize(file.size)}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-sm">
                      {formatDate(file.lastModified)}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      {file.data?.length || 0}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      {file.error ? (
                        <span className="text-red-600 text-sm">Error</span>
                      ) : (
                        <span className="text-green-600 text-sm">OK</span>
                      )}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      <Button
                        onClick={() => getFileDetails(file.fileName)}
                        variant="secondary"
                        className="text-xs px-2 py-1"
                      >
                        View Details
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* File Details Modal */}
      {selectedFile && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">File Details: {selectedFile.fileName}</h2>
            <Button
              onClick={() => setSelectedFile(null)}
              variant="secondary"
              className="text-xs"
            >
              Close
            </Button>
          </div>
          
          {selectedFile.error ? (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-700">{selectedFile.error}</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Headers ({selectedFile.headers?.length || 0})</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {selectedFile.headers?.map((header, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                    >
                      {header}
                    </span>
                  ))}
                </div>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">Data Preview (first 5 rows)</p>
                {selectedFile.data && selectedFile.data.length > 0 && (
                  <div className="mt-2 overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300 text-sm">
                      <thead>
                        <tr className="bg-gray-50">
                          {selectedFile.headers?.map((header, index) => (
                            <th key={index} className="border border-gray-300 px-2 py-1 text-left">
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {selectedFile.data.slice(0, 5).map((row, rowIndex) => (
                          <tr key={rowIndex} className="hover:bg-gray-50">
                            {selectedFile.headers?.map((header, colIndex) => (
                              <td key={colIndex} className="border border-gray-300 px-2 py-1">
                                {String(row[header] || '')}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">How to Use</h2>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>Click "Start Watching" to begin monitoring the desktop/simple_csv folder</li>
          <li>The folder will be created automatically if it doesn't exist</li>
          <li>Drop CSV files into the simple_csv folder on your desktop</li>
          <li>Files will be automatically detected and processed</li>
          <li>Click "View Details" to see the contents of any CSV file</li>
          <li>Use "Stop Watching" to stop monitoring the folder</li>
        </ol>
      </div>
    </div>
  );
}
